package com.alwan.kids2025.utils;

import android.content.Context;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.alwan.kids2025.R;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * Centralized error handling utility
 * Provides consistent error logging and user feedback
 */
public class ErrorHandler {
    private static final String TAG = "ErrorHandler";
    
    // Error types
    public enum ErrorType {
        MEMORY_ERROR("Memory Error"),
        IO_ERROR("Input/Output Error"),
        NETWORK_ERROR("Network Error"),
        PERMISSION_ERROR("Permission Error"),
        BITMAP_ERROR("Image Processing Error"),
        ADMOB_ERROR("Advertisement Error"),
        FIREBASE_ERROR("Firebase Error"),
        GENERAL_ERROR("General Error");
        
        private final String description;
        
        ErrorType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
     * Handle and log error with context
     * @param context Application context
     * @param errorType Type of error
     * @param message Error message
     * @param throwable Exception (optional)
     * @param showToUser Whether to show error to user
     */
    public static void handleError(@NonNull Context context, @NonNull ErrorType errorType, 
                                 @NonNull String message, @Nullable Throwable throwable, 
                                 boolean showToUser) {
        // Log the error
        String fullMessage = errorType.getDescription() + ": " + message;
        
        if (throwable != null) {
            Log.e(TAG, fullMessage, throwable);
            
            // Send to Firebase Crashlytics if available
            try {
                FirebaseCrashlytics crashlytics = FirebaseCrashlytics.getInstance();
                crashlytics.recordException(throwable);
                crashlytics.setCustomKey("error_type", errorType.name());
                crashlytics.setCustomKey("error_message", message);
                crashlytics.log(fullMessage);
            } catch (Exception e) {
                Log.w(TAG, "Failed to send error to Crashlytics", e);
            }
        } else {
            Log.e(TAG, fullMessage);
        }
        
        // Show user-friendly message if requested
        if (showToUser) {
            showUserError(context, errorType, message);
        }
        
        // Store error for debugging
        storeErrorForDebugging(errorType, message, throwable);
    }

    /**
     * Handle OutOfMemoryError specifically
     * @param context Application context
     * @param operation Operation that caused the error
     * @param showToUser Whether to show error to user
     */
    public static void handleOutOfMemoryError(@NonNull Context context, @NonNull String operation, 
                                            boolean showToUser) {
        String message = "Out of memory during: " + operation;
        
        // Perform memory cleanup
        MemoryUtils.performMemoryCleanup();
        
        // Log memory statistics
        Log.e(TAG, "OutOfMemoryError - " + message);
        Log.e(TAG, "Memory info: " + MemoryUtils.getMemoryInfo(context));
        
        handleError(context, ErrorType.MEMORY_ERROR, message, null, showToUser);
    }

    /**
     * Handle IO exceptions
     * @param context Application context
     * @param operation Operation that failed
     * @param exception IO exception
     * @param showToUser Whether to show error to user
     */
    public static void handleIOException(@NonNull Context context, @NonNull String operation, 
                                       @NonNull IOException exception, boolean showToUser) {
        String message = "IO operation failed: " + operation;
        handleError(context, ErrorType.IO_ERROR, message, exception, showToUser);
    }

    /**
     * Handle permission errors
     * @param context Application context
     * @param permission Permission that was denied
     * @param showToUser Whether to show error to user
     */
    public static void handlePermissionError(@NonNull Context context, @NonNull String permission, 
                                           boolean showToUser) {
        String message = "Permission denied: " + permission;
        handleError(context, ErrorType.PERMISSION_ERROR, message, null, showToUser);
    }

    /**
     * Handle bitmap processing errors
     * @param context Application context
     * @param operation Bitmap operation that failed
     * @param exception Exception (optional)
     * @param showToUser Whether to show error to user
     */
    public static void handleBitmapError(@NonNull Context context, @NonNull String operation, 
                                       @Nullable Exception exception, boolean showToUser) {
        String message = "Bitmap operation failed: " + operation;
        handleError(context, ErrorType.BITMAP_ERROR, message, exception, showToUser);
    }

    /**
     * Handle AdMob errors
     * @param context Application context
     * @param adType Type of ad (banner, interstitial, etc.)
     * @param errorCode Error code
     * @param errorMessage Error message
     */
    public static void handleAdMobError(@NonNull Context context, @NonNull String adType, 
                                      int errorCode, @NonNull String errorMessage) {
        String message = String.format("AdMob %s error (code: %d): %s", adType, errorCode, errorMessage);
        handleError(context, ErrorType.ADMOB_ERROR, message, null, false);
    }

    /**
     * Handle Firebase errors
     * @param context Application context
     * @param service Firebase service name
     * @param exception Exception
     */
    public static void handleFirebaseError(@NonNull Context context, @NonNull String service, 
                                         @NonNull Exception exception) {
        String message = "Firebase " + service + " error: " + exception.getMessage();
        handleError(context, ErrorType.FIREBASE_ERROR, message, exception, false);
    }

    /**
     * Show user-friendly error message
     * @param context Application context
     * @param errorType Type of error
     * @param message Error message
     */
    private static void showUserError(@NonNull Context context, @NonNull ErrorType errorType, 
                                    @NonNull String message) {
        String userMessage;
        
        switch (errorType) {
            case MEMORY_ERROR:
                userMessage = context.getString(R.string.error_memory);
                break;
            case IO_ERROR:
                userMessage = context.getString(R.string.error_save_load);
                break;
            case PERMISSION_ERROR:
                userMessage = context.getString(R.string.permission);
                break;
            case BITMAP_ERROR:
                userMessage = context.getString(R.string.error_image_processing);
                break;
            case NETWORK_ERROR:
                userMessage = context.getString(R.string.error_network);
                break;
            default:
                userMessage = context.getString(R.string.error_general);
                break;
        }
        
        try {
            Toast.makeText(context, userMessage, Toast.LENGTH_LONG).show();
        } catch (Exception e) {
            Log.e(TAG, "Failed to show error toast", e);
        }
    }

    /**
     * Store error information for debugging
     * @param errorType Type of error
     * @param message Error message
     * @param throwable Exception (optional)
     */
    private static void storeErrorForDebugging(@NonNull ErrorType errorType, @NonNull String message, 
                                             @Nullable Throwable throwable) {
        if (SecurityUtils.isDebugMode()) {
            StringBuilder errorInfo = new StringBuilder();
            errorInfo.append("Error Type: ").append(errorType.name()).append("\n");
            errorInfo.append("Message: ").append(message).append("\n");
            errorInfo.append("Timestamp: ").append(System.currentTimeMillis()).append("\n");
            
            if (throwable != null) {
                errorInfo.append("Stack Trace:\n");
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                throwable.printStackTrace(pw);
                errorInfo.append(sw.toString());
            }
            
            // Store in secure storage for debugging
            String key = "error_" + System.currentTimeMillis();
            SecurityUtils.storeSecureData(key, errorInfo.toString());
        }
    }

    /**
     * Get stack trace as string
     * @param throwable Exception
     * @return Stack trace string
     */
    @NonNull
    public static String getStackTraceString(@NonNull Throwable throwable) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }

    /**
     * Log performance warning
     * @param operation Operation name
     * @param duration Duration in milliseconds
     */
    public static void logPerformanceWarning(@NonNull String operation, long duration) {
        if (duration > 1000) { // More than 1 second
            Log.w(TAG, "Performance warning: " + operation + " took " + duration + "ms");
        }
    }

    /**
     * Log memory warning
     * @param context Application context
     * @param operation Operation that might cause memory issues
     */
    public static void logMemoryWarning(@NonNull Context context, @NonNull String operation) {
        if (MemoryUtils.isLowMemory(context)) {
            Log.w(TAG, "Memory warning: Low memory detected during " + operation);
            Log.w(TAG, MemoryUtils.getMemoryInfo(context));
        }
    }
}
