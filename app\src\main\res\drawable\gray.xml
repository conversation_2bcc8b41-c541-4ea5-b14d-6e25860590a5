<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="@color/gray" />
            <stroke android:width="2dp" android:color="@color/black" />
        </shape>
    </item>
    <item android:state_enabled="false">
        <shape android:shape="oval">
            <solid android:color="@color/gray" />
            <stroke android:width="2dp" android:color="@color/black" />
        </shape>
    </item>
    <item android:state_selected="true">
        <shape android:shape="oval">
            <solid android:color="@color/gray" />
            <stroke android:width="2dp" android:color="@color/black" />
        </shape>
    </item>
    <item>
        <shape android:shape="oval">
            <solid android:color="@color/gray" />
            <stroke android:width="1dp" android:color="@color/divider" />
        </shape>
    </item>
</selector>