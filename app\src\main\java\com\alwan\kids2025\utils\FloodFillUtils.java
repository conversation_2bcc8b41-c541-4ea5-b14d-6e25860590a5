package com.alwan.kids2025.utils;

import android.graphics.Bitmap;
import android.graphics.Point;
import android.util.Log;

import androidx.annotation.NonNull;

import java.util.ArrayDeque;
import java.util.Queue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * Optimized Flood Fill utility class
 * Provides efficient flood fill algorithm with memory optimization
 */
public class FloodFillUtils {
    private static final String TAG = "FloodFillUtils";
    private static final int MAX_QUEUE_SIZE = 100000; // Prevent excessive memory usage
    private static ExecutorService executorService;

    /**
     * Initialize the flood fill executor service
     */
    public static void initialize() {
        if (executorService == null || executorService.isShutdown()) {
            executorService = Executors.newSingleThreadExecutor();
            Log.d(TAG, "FloodFill executor service initialized");
        }
    }

    /**
     * Shutdown the executor service
     */
    public static void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            Log.d(TAG, "FloodFill executor service shutdown");
        }
    }

    /**
     * Perform flood fill operation asynchronously
     * @param bitmap Target bitmap
     * @param startPoint Starting point
     * @param targetColor Color to replace
     * @param replacementColor New color
     * @param callback Callback for completion
     * @return Future for the operation
     */
    public static Future<?> floodFillAsync(@NonNull Bitmap bitmap, @NonNull Point startPoint,
                                          int targetColor, int replacementColor,
                                          @NonNull FloodFillCallback callback) {
        if (executorService == null) {
            initialize();
        }

        return executorService.submit(() -> {
            try {
                boolean success = floodFill(bitmap, startPoint, targetColor, replacementColor);
                callback.onFloodFillComplete(success);
            } catch (Exception e) {
                Log.e(TAG, "Error during flood fill operation", e);
                callback.onFloodFillComplete(false);
            }
        });
    }

    /**
     * Optimized flood fill algorithm using scanline approach
     * @param bitmap Target bitmap
     * @param startPoint Starting point
     * @param targetColor Color to replace
     * @param replacementColor New color
     * @return true if operation successful
     */
    public static boolean floodFill(@NonNull Bitmap bitmap, @NonNull Point startPoint,
                                   int targetColor, int replacementColor) {
        if (bitmap.isRecycled()) {
            Log.e(TAG, "Cannot perform flood fill on recycled bitmap");
            return false;
        }

        if (targetColor == replacementColor) {
            Log.d(TAG, "Target and replacement colors are the same, skipping flood fill");
            return true;
        }

        int width = bitmap.getWidth();
        int height = bitmap.getHeight();

        if (startPoint.x < 0 || startPoint.x >= width || startPoint.y < 0 || startPoint.y >= height) {
            Log.e(TAG, "Start point is outside bitmap bounds");
            return false;
        }

        if (bitmap.getPixel(startPoint.x, startPoint.y) != targetColor) {
            Log.d(TAG, "Start point does not match target color");
            return false;
        }

        try {
            return performScanlineFloodFill(bitmap, startPoint, targetColor, replacementColor, width, height);
        } catch (OutOfMemoryError e) {
            Log.e(TAG, "OutOfMemoryError during flood fill", e);
            System.gc(); // Suggest garbage collection
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error during flood fill", e);
            return false;
        }
    }

    /**
     * Scanline flood fill implementation for better performance
     * @param bitmap Target bitmap
     * @param startPoint Starting point
     * @param targetColor Color to replace
     * @param replacementColor New color
     * @param width Bitmap width
     * @param height Bitmap height
     * @return true if successful
     */
    private static boolean performScanlineFloodFill(@NonNull Bitmap bitmap, @NonNull Point startPoint,
                                                   int targetColor, int replacementColor,
                                                   int width, int height) {
        Queue<Point> queue = new ArrayDeque<>();
        queue.offer(new Point(startPoint.x, startPoint.y));
        int processedPixels = 0;

        while (!queue.isEmpty() && queue.size() < MAX_QUEUE_SIZE) {
            Point point = queue.poll();
            if (point == null) break;

            int x = point.x;
            int y = point.y;

            // Find the leftmost pixel in this row that matches target color
            while (x > 0 && bitmap.getPixel(x - 1, y) == targetColor) {
                x--;
            }

            boolean spanUp = false;
            boolean spanDown = false;

            // Fill the scanline and check adjacent rows
            while (x < width && bitmap.getPixel(x, y) == targetColor) {
                bitmap.setPixel(x, y, replacementColor);
                processedPixels++;

                // Check pixel above
                if (!spanUp && y > 0 && bitmap.getPixel(x, y - 1) == targetColor) {
                    queue.offer(new Point(x, y - 1));
                    spanUp = true;
                } else if (spanUp && y > 0 && bitmap.getPixel(x, y - 1) != targetColor) {
                    spanUp = false;
                }

                // Check pixel below
                if (!spanDown && y < height - 1 && bitmap.getPixel(x, y + 1) == targetColor) {
                    queue.offer(new Point(x, y + 1));
                    spanDown = true;
                } else if (spanDown && y < height - 1 && bitmap.getPixel(x, y + 1) != targetColor) {
                    spanDown = false;
                }

                x++;
            }

            // Prevent infinite loops and excessive memory usage
            if (processedPixels > width * height) {
                Log.w(TAG, "Flood fill processed too many pixels, stopping");
                break;
            }
        }

        if (queue.size() >= MAX_QUEUE_SIZE) {
            Log.w(TAG, "Flood fill queue size exceeded maximum, operation may be incomplete");
        }

        Log.d(TAG, "Flood fill completed, processed " + processedPixels + " pixels");
        return true;
    }

    /**
     * Check if a point is valid for flood fill
     * @param bitmap Target bitmap
     * @param point Point to check
     * @param targetColor Expected color at point
     * @return true if point is valid
     */
    public static boolean isValidFloodFillPoint(@NonNull Bitmap bitmap, @NonNull Point point, int targetColor) {
        if (bitmap.isRecycled()) {
            return false;
        }

        int width = bitmap.getWidth();
        int height = bitmap.getHeight();

        if (point.x < 0 || point.x >= width || point.y < 0 || point.y >= height) {
            return false;
        }

        return bitmap.getPixel(point.x, point.y) == targetColor;
    }

    /**
     * Get the color at a specific point in the bitmap
     * @param bitmap Target bitmap
     * @param point Point to check
     * @return Color at point or -1 if invalid
     */
    public static int getColorAtPoint(@NonNull Bitmap bitmap, @NonNull Point point) {
        if (bitmap.isRecycled()) {
            return -1;
        }

        int width = bitmap.getWidth();
        int height = bitmap.getHeight();

        if (point.x < 0 || point.x >= width || point.y < 0 || point.y >= height) {
            return -1;
        }

        try {
            return bitmap.getPixel(point.x, point.y);
        } catch (Exception e) {
            Log.e(TAG, "Error getting color at point", e);
            return -1;
        }
    }

    /**
     * Estimate the area that would be affected by flood fill
     * @param bitmap Target bitmap
     * @param startPoint Starting point
     * @param targetColor Color to replace
     * @return Estimated number of pixels that would be affected
     */
    public static int estimateFloodFillArea(@NonNull Bitmap bitmap, @NonNull Point startPoint, int targetColor) {
        if (!isValidFloodFillPoint(bitmap, startPoint, targetColor)) {
            return 0;
        }

        // Simple estimation using a limited BFS
        Queue<Point> queue = new ArrayDeque<>();
        boolean[][] visited = new boolean[bitmap.getWidth()][bitmap.getHeight()];
        queue.offer(startPoint);
        visited[startPoint.x][startPoint.y] = true;

        int count = 0;
        int maxEstimation = 1000; // Limit estimation to prevent performance issues

        while (!queue.isEmpty() && count < maxEstimation) {
            Point point = queue.poll();
            if (point == null) break;

            count++;

            // Check 4-connected neighbors
            int[] dx = {-1, 1, 0, 0};
            int[] dy = {0, 0, -1, 1};

            for (int i = 0; i < 4; i++) {
                int newX = point.x + dx[i];
                int newY = point.y + dy[i];

                if (newX >= 0 && newX < bitmap.getWidth() && 
                    newY >= 0 && newY < bitmap.getHeight() && 
                    !visited[newX][newY] && 
                    bitmap.getPixel(newX, newY) == targetColor) {
                    
                    visited[newX][newY] = true;
                    queue.offer(new Point(newX, newY));
                }
            }
        }

        return count;
    }

    /**
     * Callback interface for flood fill operations
     */
    public interface FloodFillCallback {
        void onFloodFillComplete(boolean success);
    }
}
