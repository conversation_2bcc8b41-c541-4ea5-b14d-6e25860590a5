package com.alwan.kids2025;

import static com.google.android.gms.ads.RequestConfiguration.MAX_AD_CONTENT_RATING_G;
import static com.google.android.gms.ads.RequestConfiguration.TAG_FOR_CHILD_DIRECTED_TREATMENT_TRUE;

import android.Manifest;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Point;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.media.MediaPlayer;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.StrictMode;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.View;
import android.view.WindowManager;
import android.view.WindowMetrics;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Toast;

import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.preference.PreferenceManager;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.RequestConfiguration;
import com.google.android.gms.ads.initialization.InitializationStatus;
import com.google.android.gms.ads.initialization.OnInitializationCompleteListener;
import com.google.android.gms.ads.appopen.AppOpenAd;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.material.snackbar.Snackbar;
import com.onesignal.OneSignal;
import com.flask.colorpicker.ColorPickerView;
import com.flask.colorpicker.OnColorSelectedListener;
import com.flask.colorpicker.builder.ColorPickerClickListener;
import com.flask.colorpicker.builder.ColorPickerDialogBuilder;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.Queue;
import java.util.Random;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.alwan.kids2025.CategoryItems;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.onesignal.OSDeviceState;

public class MainActivity extends AppCompatActivity implements View.OnTouchListener, View.OnClickListener {

    private static final int PERMISSION_REQUEST_CODE = 1;
    public static ArrayList<Point> drawnPoints = new ArrayList<>();
    public static boolean readyForReview = false;
    static String randomNumber;
    Paint paint;
    ImageView iv;
    int position, code;
    Button White, Black, Gray, lightOrange, Brown, Yellow, deepBlue, lightBlue, deepPurple, lightPurple,
            deepGreen, lightGreen, deepPink, lightPink, Red, deepOrange, select_color;
    int h, w;
    AdView mAdView;
    SharedPreferences preferences;
    MediaPlayer mPlayer, clickPlayer;
    int counter = 0;
    Point aaa;
    private RelativeLayout drawingLayout;
    private MyView myView;
    private ArrayList<Path> paths = new ArrayList<>();
    private ArrayList<Path> undonePaths = new ArrayList<>();
    private FirebaseAnalytics mFirebaseAnalytics;
    private AppOpenAd appOpenAd;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // Initialize Firebase Analytics
        mFirebaseAnalytics = FirebaseAnalytics.getInstance(this);

        // Log an event to test Firebase Analytics
        if (mFirebaseAnalytics != null) {
            Bundle bundle = new Bundle();
            bundle.putString(FirebaseAnalytics.Param.METHOD, "onCreate");
            mFirebaseAnalytics.logEvent(FirebaseAnalytics.Event.APP_OPEN, bundle);
        } else {
            Log.e("MainActivity", "FirebaseAnalytics initialization failed.");
        }

        // استخراج البيانات الممررة وتسجيل OnBackPressed callback باستخدام OnBackPressedDispatcher
        Bundle extras = getIntent().getExtras();
        if (extras != null) {
            position = extras.getInt("position");
            code = extras.getInt("code");
        }
        getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                Intent intent = new Intent(MainActivity.this, CategoryItems.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.putExtra("code", code);
                startActivity(intent);
            }
        });

        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        randomNumber = String.valueOf(new Random().nextInt(5) + 1);

        StrictMode.VmPolicy.Builder builder = new StrictMode.VmPolicy.Builder();
        StrictMode.setVmPolicy(builder.build());

        preferences = PreferenceManager.getDefaultSharedPreferences(MainActivity.this);

        MobileAds.initialize(this, new OnInitializationCompleteListener() {
            @Override
            public void onInitializationComplete(InitializationStatus initializationStatus) { }
        });
        RequestConfiguration requestConfiguration = MobileAds.getRequestConfiguration()
                .toBuilder()
                .setTagForChildDirectedTreatment(TAG_FOR_CHILD_DIRECTED_TREATMENT_TRUE)
                .setMaxAdContentRating(MAX_AD_CONTENT_RATING_G)
                .build();
        MobileAds.setRequestConfiguration(requestConfiguration);

        mAdView = findViewById(R.id.adView);
        AdRequest adRequest = new AdRequest.Builder().build();
        mAdView.loadAd(adRequest);

        iv = findViewById(R.id.coringImage);

        myView = new MyView(this, getWindowManager());
        drawingLayout = findViewById(R.id.relative_layout);
        drawingLayout.addView(myView);

        White = findViewById(R.id.white);
        Black = findViewById(R.id.black);
        Gray = findViewById(R.id.gray);
        lightOrange = findViewById(R.id.light_orange);
        Brown = findViewById(R.id.brown);
        Yellow = findViewById(R.id.yellow);
        deepBlue = findViewById(R.id.deep_blue);
        lightBlue = findViewById(R.id.light_blue);
        deepPurple = findViewById(R.id.deep_purple);
        lightPurple = findViewById(R.id.light_purple);
        deepGreen = findViewById(R.id.deep_green);
        lightGreen = findViewById(R.id.light_green);
        deepPink = findViewById(R.id.deep_pink);
        lightPink = findViewById(R.id.light_pink);
        Red = findViewById(R.id.red);
        deepOrange = findViewById(R.id.deep_orange);
        select_color = findViewById(R.id.select_color);

        White.setOnClickListener(this);
        Black.setOnClickListener(this);
        Gray.setOnClickListener(this);
        lightOrange.setOnClickListener(this);
        Brown.setOnClickListener(this);
        Yellow.setOnClickListener(this);
        deepBlue.setOnClickListener(this);
        lightBlue.setOnClickListener(this);
        deepPurple.setOnClickListener(this);
        lightPurple.setOnClickListener(this);
        deepGreen.setOnClickListener(this);
        lightGreen.setOnClickListener(this);
        deepPink.setOnClickListener(this);
        lightPink.setOnClickListener(this);
        Red.setOnClickListener(this);
        deepOrange.setOnClickListener(this);
        select_color.setOnClickListener(this);

        Typeface tf = Typeface.createFromAsset(getAssets(), "fonts/Blabeloo.ttf");
        White.setTypeface(tf);
        Black.setTypeface(tf);
        Gray.setTypeface(tf);
        lightOrange.setTypeface(tf);
        Brown.setTypeface(tf);
        Yellow.setTypeface(tf);
        deepBlue.setTypeface(tf);
        lightBlue.setTypeface(tf);
        deepPurple.setTypeface(tf);
        lightPurple.setTypeface(tf);
        deepGreen.setTypeface(tf);
        lightGreen.setTypeface(tf);
        deepPink.setTypeface(tf);
        lightPink.setTypeface(tf);
        Red.setTypeface(tf);
        deepOrange.setTypeface(tf);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED) {
                Log.w("Permissions", "POST_NOTIFICATIONS permission not granted. Requesting permission...");
                ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.POST_NOTIFICATIONS}, 100);
            } else {
                Log.d("Permissions", "POST_NOTIFICATIONS permission already granted.");
            }
        }

        MyFirebaseMessagingService.initialize(this);
        // Log OneSignal initialization start
        Log.d("OneSignal", "Initializing OneSignal...");

        // Initialize OneSignal
        OneSignal.initWithContext(this);
        // Replace placeholder App ID with the actual OneSignal App ID
        OneSignal.setAppId("************************************"); // Replace with your actual OneSignal App ID

        // Add logs to track OneSignal initialization
        Log.d("OneSignal", "Initializing OneSignal...");

        // Check if OneSignal is initialized successfully
        OneSignal.setLogLevel(OneSignal.LOG_LEVEL.VERBOSE, OneSignal.LOG_LEVEL.NONE);

        // Add a notification received handler with detailed logging
        OneSignal.setNotificationWillShowInForegroundHandler(notificationReceivedEvent -> {
            Log.d("OneSignal", "Notification received: " + notificationReceivedEvent.getNotification().getBody());
            notificationReceivedEvent.complete(notificationReceivedEvent.getNotification());
        });

        // Add a notification opened handler with detailed logging
        OneSignal.setNotificationOpenedHandler(result -> {
            Log.d("OneSignal", "Notification opened: " + result.getNotification().getBody());
        });

        // Log OneSignal device state with additional checks
        OSDeviceState deviceState = OneSignal.getDeviceState();
        if (deviceState != null) {
            Log.d("OneSignal", "Device ID: " + deviceState.getUserId());
            Log.d("OneSignal", "Subscribed: " + deviceState.isSubscribed());
        } else {
            Log.e("OneSignal", "Device state is null. Initialization might have failed.");
        }

        // Ensure notification permission is requested correctly
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED) {
                Log.d("MainActivity", "Requesting POST_NOTIFICATIONS permission...");
                ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.POST_NOTIFICATIONS}, 100);
            } else {
                Log.d("MainActivity", "POST_NOTIFICATIONS permission already granted.");
            }
        } else {
            Log.d("MainActivity", "POST_NOTIFICATIONS permission not required for this Android version.");
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        loadAppOpenAd();
    }

    private void loadAppOpenAd() {
        AdRequest adRequest = new AdRequest.Builder().build();
        AppOpenAd.load(
            this,
            getString(R.string.app_open_ad_unit_id),
            adRequest,
            new AppOpenAd.AppOpenAdLoadCallback() {
                @Override
                public void onAdLoaded(AppOpenAd ad) {
                    appOpenAd = ad;
                    showAppOpenAd();
                }

                @Override
                public void onAdFailedToLoad(LoadAdError loadAdError) {
                    Log.e("AppOpenAd", "Failed to load: " + loadAdError.getMessage());
                }
            });
    }

    private void showAppOpenAd() {
        if (appOpenAd != null) {
            appOpenAd.show(this);
        }
    }

    private void requestNotificationPermission() {
        // Add logs to debug notification permission handling
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED) {
                Log.d("MainActivity", "Notification permission not granted. Requesting permission...");
                ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.POST_NOTIFICATIONS}, 100);
            } else {
                Log.d("MainActivity", "Notification permission already granted.");
            }
        } else {
            Log.d("MainActivity", "Notification permission not required for this Android version.");
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == 100) { // Notification permission request code
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, getString(R.string.notification_permission_granted), Toast.LENGTH_SHORT).show();
                Log.d("MainActivity", "Notification permission granted.");
            } else {
                Toast.makeText(this, getString(R.string.notification_permission_denied), Toast.LENGTH_SHORT).show();
                Log.d("MainActivity", "Notification permission denied.");
            }
        }
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        return false;
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.share_save_menu, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.action_share:
                onShareImageItem();
                break;
            case R.id.action_save:
                save(myView);
                break;
            case R.id.action_mute:
                if (mPlayer != null) {
                    if (!mPlayer.isPlaying()) {
                        mPlayer.start();
                        mPlayer.setLooping(true);
                        item.setIcon(R.drawable.sound_on);
                        Snackbar.make(drawingLayout, getString(R.string.soundon), Snackbar.LENGTH_LONG).show();
                    } else {
                        mPlayer.pause();
                        item.setIcon(R.drawable.sound_off);
                        Snackbar.make(drawingLayout, getString(R.string.soundoff), Snackbar.LENGTH_LONG).show();
                    }
                }
                break;
            case R.id.action_undo:
                myView.undoMethod();
                break;
            case android.R.id.home:
                Intent i = new Intent(MainActivity.this, CategoryItems.class);
                i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                i.putExtra("code", code);
                startActivity(i);
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    public void onShareImageItem() {
        String package_name = getPackageName();
        Uri bmpUri = getLocalBitmapUri(iv);
        if (bmpUri != null) {
            Intent shareIntent = new Intent();
            shareIntent.setAction(Intent.ACTION_SEND);
            shareIntent.putExtra(Intent.EXTRA_STREAM, bmpUri);
            shareIntent.putExtra(Intent.EXTRA_TEXT, "شفتوا إبداعي؟!\nأنا رسمت هذه الرسمة باستخدام تطبيق ألوان الأطفال! 🎨💛\nفيه أدوات ممتعة وألوان جميلة تخليك ترسم وتلون بطريقتك الخاصة!\nحمّل التطبيق وورينا إبداعك 👇\nhttps://play.google.com/store/apps/details?id=" + package_name);
            shareIntent.setType("image/*");
            startActivity(Intent.createChooser(shareIntent, getString(R.string.action_share)));
        }
    }

    // Removed the catch block for IOException in the getLocalBitmapUri method
    public Uri getLocalBitmapUri(ImageView iv) {
        iv.setImageBitmap(myView.scaledBitmap);
        Drawable drawable = iv.getDrawable();
        Bitmap bmp = null;
        if (drawable instanceof BitmapDrawable) {
            bmp = ((BitmapDrawable) iv.getDrawable()).getBitmap();
        } else {
            return null;
        }
        Uri bmpUri = null;
        File file;
        try {
            File dir = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).getPath()
                    + "/" + getString(R.string.app_name) + "/");
            dir.mkdirs();
            String fileName = "/" + System.currentTimeMillis() + "share_image.png";
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                file = new File(dir, fileName);
            } else {
                file = new File(getExternalFilesDir(Environment.DIRECTORY_PICTURES),
                        "share_image_" + System.currentTimeMillis() + ".png");
            }
            // Convert to try-with-resources for better resource management
            try (FileOutputStream out = new FileOutputStream(file)) {
                bmp.compress(Bitmap.CompressFormat.PNG, 90, out);
            }
            bmpUri = Uri.fromFile(file);
        } catch (Exception e) {
            Log.e("MainActivity", "Error occurred", e);
        }
        return bmpUri;
    }

    public void save(View view) {
        File filename;
        iv.setImageBitmap(myView.scaledBitmap);
        try {
            File appDirectory = new File(getFilesDir(), "تلوين كتاب الأطفال");
            if (!appDirectory.exists()) {
                appDirectory.mkdirs();
            }
            File savedFile = new File(appDirectory, "example.txt");
            try (FileOutputStream fos = new FileOutputStream(savedFile)) {
                fos.write("Hello, Color Trip!".getBytes());
            }

            File dir = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).getPath()
                    + "/" + getString(R.string.app_name) + "/");
            dir.mkdirs();
            String fileName = "/" + System.currentTimeMillis() + "image.jpg";
            filename = new File(dir, fileName);
            FileOutputStream fos = new FileOutputStream(filename);
            myView.scaledBitmap.compress(Bitmap.CompressFormat.PNG, 100, fos);
            fos.flush();
            fos.close();

            Toast.makeText(getApplicationContext(), getString(R.string.save), Toast.LENGTH_LONG).show();
            MediaScannerConnection.scanFile(MainActivity.this,
                    new String[]{filename.toString()}, null,
                    new MediaScannerConnection.OnScanCompletedListener() {
                        public void onScanCompleted(String path, Uri uri) {
                            Log.i("ExternalStorage", "Scanned " + path + ":");
                            Log.i("ExternalStorage", "-> uri=" + uri);
                        }
                    });
        } catch (Exception e) {
            Log.e("MainActivity", "Error occurred", e);
        }
    }

    @Override
    public void onClick(View view) {
        enable();
        // Convert switch to enhanced switch
        switch (view.getId()) {
            case R.id.white -> {
                paint.setColor(ContextCompat.getColor(this, R.color.white));
                White.setSelected(true);
            }
            case R.id.black -> {
                paint.setColor(ContextCompat.getColor(this, R.color.black));
                Black.setSelected(true);
            }
            case R.id.gray -> {
                paint.setColor(ContextCompat.getColor(this, R.color.gray));
                Gray.setSelected(true);
            }
            case R.id.light_orange -> {
                paint.setColor(ContextCompat.getColor(this, R.color.light_orange));
                lightOrange.setSelected(true);
            }
            case R.id.brown -> {
                paint.setColor(ContextCompat.getColor(this, R.color.brown));
                Brown.setSelected(true);
            }
            case R.id.yellow -> {
                paint.setColor(ContextCompat.getColor(this, R.color.yellow));
                Yellow.setSelected(true);
            }
            case R.id.deep_blue -> {
                paint.setColor(ContextCompat.getColor(this, R.color.deep_blue));
                deepBlue.setSelected(true);
            }
            case R.id.light_blue -> {
                paint.setColor(ContextCompat.getColor(this, R.color.light_blue));
                lightBlue.setSelected(true);
            }
            case R.id.deep_purple -> {
                paint.setColor(ContextCompat.getColor(this, R.color.deep_purple));
                deepPurple.setSelected(true);
            }
            case R.id.light_purple -> {
                paint.setColor(ContextCompat.getColor(this, R.color.light_purple));
                lightPurple.setSelected(true);
            }
            case R.id.deep_green -> {
                paint.setColor(ContextCompat.getColor(this, R.color.deep_green));
                deepGreen.setSelected(true);
            }
            case R.id.light_green -> {
                paint.setColor(ContextCompat.getColor(this, R.color.light_green));
                lightGreen.setSelected(true);
            }
            case R.id.deep_pink -> {
                paint.setColor(ContextCompat.getColor(this, R.color.deep_pink));
                deepPink.setSelected(true);
            }
            case R.id.light_pink -> {
                paint.setColor(ContextCompat.getColor(this, R.color.light_pink));
                lightPink.setSelected(true);
            }
            case R.id.red -> {
                paint.setColor(ContextCompat.getColor(this, R.color.red));
                Red.setSelected(true);
            }
            case R.id.deep_orange -> {
                paint.setColor(ContextCompat.getColor(this, R.color.deep_orange));
                deepOrange.setSelected(true);
            }
            case R.id.select_color -> {
                ColorPickerDialogBuilder
                        .with(this)
                        .setTitle(getString(R.string.chooseColor))
                        .wheelType(ColorPickerView.WHEEL_TYPE.FLOWER)
                        .density(12)
                        .showLightnessSlider(false)
                        .setOnColorSelectedListener(new OnColorSelectedListener() {
                            @Override
                            public void onColorSelected(int selectedColor) {
                                // منطق إضافي إذا لزم
                            }
                        })
                        .setPositiveButton(getString(R.string.ok), new ColorPickerClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int selectedColor, Integer[] allColors) {
                                paint.setColor(selectedColor);
                            }
                        })
                        .setNegativeButton(getString(R.string.cancel), new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) { }
                        })
                        .build()
                        .show();
            }
        }
    }

    public void enable() {
        White.setSelected(false);
        Gray.setSelected(false);
        Black.setSelected(false);
        lightOrange.setSelected(false);
        Brown.setSelected(false);
        Yellow.setSelected(false);
        deepBlue.setSelected(false);
        lightBlue.setSelected(false);
        deepPurple.setSelected(false);
        lightPurple.setSelected(false);
        deepGreen.setSelected(false);
        lightGreen.setSelected(false);
        deepPink.setSelected(false);
        lightPink.setSelected(false);
        Red.setSelected(false);
        deepOrange.setSelected(false);
    }

    private boolean checkPermission() {
        int result = ContextCompat.checkSelfPermission(MainActivity.this, Manifest.permission.WRITE_EXTERNAL_STORAGE);
        return result == PackageManager.PERMISSION_GRANTED;
    }

    private void requestPermission() {
        if (ActivityCompat.shouldShowRequestPermissionRationale(MainActivity.this, Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
            Toast.makeText(MainActivity.this, getString(R.string.permission), Toast.LENGTH_LONG).show();
        } else {
            ActivityCompat.requestPermissions(MainActivity.this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, PERMISSION_REQUEST_CODE);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mPlayer != null && !mPlayer.isPlaying()) {
            mPlayer.start();
            mPlayer.setLooping(true);
        } else {
            int backgroundResourceId = this.getResources().getIdentifier("background_" + randomNumber, "raw", this.getPackageName());
            mPlayer = MediaPlayer.create(MainActivity.this, backgroundResourceId);
            mPlayer.start();
            mPlayer.setLooping(true);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        readyForReview = true;
        if (mPlayer != null && mPlayer.isPlaying()) {
            mPlayer.pause();
        }
    }

    // ============================
    // الكلاس الداخلي MyView مع خاصية Flood Fill
    // ============================
    // نضع @SuppressWarnings على مستوى المُنشئ بدلاً من الأقسام المحلية
    @SuppressWarnings("deprecation")
    public class MyView extends View {
        Point p1 = new Point();
        Bitmap mBitmap, scaledBitmap;
        Canvas canvas;
        private final Path path;
        private final ScaleGestureDetector scaleDetector;
        private float scaleFactor = 1.f;
        private float mPositionX, mPositionY;
        private float refX, refY;
        private final WindowManager windowManager;

        @SuppressWarnings("deprecation")
        public MyView(Context context, WindowManager windowManager) {
            super(context);
            this.windowManager = windowManager;
            this.path = new Path();
            paint = new Paint();
            paint.setAntiAlias(true);
            paint.setStyle(Paint.Style.STROKE);
            paint.setStrokeJoin(Paint.Join.ROUND);
            paint.setStrokeWidth(5f);

            int id = getResources().getIdentifier("gp" + code + "_" + position, "drawable", getPackageName());
            mBitmap = BitmapFactory.decodeResource(getResources(), id).copy(Bitmap.Config.ARGB_8888, true);

            DisplayMetrics displayMetrics = new DisplayMetrics();
            updateDisplayMetrics(displayMetrics);

            int width, height;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                WindowMetrics metrics = windowManager.getCurrentWindowMetrics();
                Rect bounds = metrics.getBounds();
                width = bounds.width();
                height = bounds.height();
            } else {
                Display display = windowManager.getDefaultDisplay();
                Point size = new Point();
                display.getSize(size);
                width = size.x;
                height = size.y;
            }

            int orientation = Configuration.ORIENTATION_UNDEFINED;
            if (width == height) {
                orientation = Configuration.ORIENTATION_UNDEFINED;
            } else if (width < height) {
                orientation = Configuration.ORIENTATION_PORTRAIT;
            } else {
                orientation = Configuration.ORIENTATION_LANDSCAPE;
            }

            if (orientation == Configuration.ORIENTATION_PORTRAIT) {
                h = displayMetrics.heightPixels / 2 + 100;
                w = displayMetrics.widthPixels;
            } else if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
                // معالجة خاصة إذا لزم الأمر لبعض الحالات
            }

            scaledBitmap = Bitmap.createScaledBitmap(mBitmap, w, h, false);
            scaleDetector = new ScaleGestureDetector(context, new ScaleListener());
        }

        public void undoMethod() {
            if (!paths.isEmpty()) {
                counter++;
                if (!paths.isEmpty()) {
                    undonePaths.add(paths.remove(paths.size() - 1));
                    invalidate();
                    aaa = drawnPoints.get(drawnPoints.size() - counter);
                    int xxxx = aaa.x;
                    int yyyy = aaa.y;
                    if (yyyy >= 0 && yyyy < MyView.this.scaledBitmap.getHeight() && xxxx >= 0) {
                        final int sourceColor = MyView.this.scaledBitmap.getPixel(xxxx, yyyy);
                        final int targetColor = Color.TRANSPARENT;
                        ExecutorService executor = Executors.newSingleThreadExecutor();
                        executor.execute(() -> new TheTask(scaledBitmap, aaa, sourceColor, targetColor).run());
                    }
                }
            } else {
                drawnPoints.clear();
                paths.clear();
                undonePaths.clear();
                counter = 0;
                Toast.makeText(MainActivity.this, getString(R.string.nomore), Toast.LENGTH_SHORT).show();
                Intent intent = new Intent(MainActivity.this, MainActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NO_ANIMATION);
                intent.putExtra("position", position);
                intent.putExtra("code", code);
                startActivity(intent);
            }
        }

        @Override
        protected void onDraw(Canvas canvas) {
            canvas.save();
            this.canvas = canvas;
            canvas.translate(mPositionX, mPositionY);
            canvas.scale(scaleFactor, scaleFactor);
            DisplayMetrics metrics = new DisplayMetrics();
            updateDisplayMetrics(metrics);
            canvas.drawBitmap(scaledBitmap, 0, 0, paint);
            for (Path p : paths) {
                canvas.drawPath(p, paint);
            }
            canvas.restore();
        }

        @Override
        public boolean onTouchEvent(MotionEvent event) {
            scaleDetector.onTouchEvent(event);
            DisplayMetrics displaymetrics = new DisplayMetrics();
            updateDisplayMetrics(displaymetrics);
            if (event.getAction() == MotionEvent.ACTION_UP) {
                try {
                    if (clickPlayer != null && !clickPlayer.isPlaying()) {
                        clickPlayer.start();
                    } else {
                        clickPlayer = MediaPlayer.create(MainActivity.this, R.raw.click);
                        clickPlayer.start();
                    }
                    p1 = new Point();
                    p1.x = (int) ((refX - mPositionX) / scaleFactor);
                    p1.y = (int) ((refY - mPositionY) / scaleFactor);
                    drawnPoints.add(p1);
                    final int sourceColor = scaledBitmap.getPixel((int) ((refX - mPositionX) / scaleFactor),
                            (int) ((refY - mPositionY) / scaleFactor));
                    final int targetColor = paint.getColor();
                    if (sourceColor != targetColor) {
                        ExecutorService executor = Executors.newSingleThreadExecutor();
                        executor.execute(() -> new TheTask(scaledBitmap, p1, sourceColor, targetColor).run());
                    } else {
                        Paint feedbackPaint = new Paint();
                        feedbackPaint.setStyle(Paint.Style.STROKE);
                        feedbackPaint.setStrokeWidth(5);
                        feedbackPaint.setColor(Color.WHITE);
                        canvas.drawCircle(p1.x, p1.y, 20, feedbackPaint);
                        invalidate();
                    }
                    paths.add(path);
                    invalidate();
                } catch (Exception e) {
                    Log.e("MainActivity", "Error occurred", e);
                }
                return true;
            } else if (event.getAction() == MotionEvent.ACTION_DOWN) {
                refX = event.getX();
                refY = event.getY();
                return true;
            } else if (event.getAction() == MotionEvent.ACTION_MOVE) {
                if (scaleFactor > 1.f) {
                    float nX = event.getX();
                    float nY = event.getY();
                    mPositionX += nX - refX;
                    mPositionY += nY - refY;
                    refX = nX;
                    refY = nY;
                    invalidate();
                    return true;
                } else {
                    return false;
                }
            } else if (event.getActionMasked() == MotionEvent.ACTION_CANCEL) {
                return true;
            } else if (event.getActionMasked() == MotionEvent.ACTION_POINTER_UP) {
                return true;
            }
            return super.onTouchEvent(event);
        }

        @Override
        protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec);
            w = widthMeasureSpec - MeasureSpec.EXACTLY;
            h = heightMeasureSpec - MeasureSpec.EXACTLY;
            Log.d("Dim", w + " | " + h);
        }

        private class ScaleListener extends ScaleGestureDetector.SimpleOnScaleGestureListener {
            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                scaleFactor *= detector.getScaleFactor();
                scaleFactor = Math.max(1.f, Math.min(scaleFactor, 5.0f));
                invalidate();
                return true;
            }
        }

        class TheTask implements Runnable {
            Bitmap bmp;
            Point pt;
            int replacementColor, targetColor;
            public TheTask(Bitmap bm, Point p, int sc, int tc) {
                this.bmp = bm;
                this.pt = p;
                this.replacementColor = tc;
                this.targetColor = sc;
            }
            @Override
            public void run() {
                Log.d("FloodFill", "Target Color: " + targetColor + ", Replacement Color: " + replacementColor);
                Log.d("FloodFill", "Point: " + pt.x + ", " + pt.y);
                FloodFill f = new FloodFill();
                f.floodFill(bmp, pt, targetColor, replacementColor);
                postInvalidate();
            }
        }
    }

    public class FloodFill {
        public void floodFill(Bitmap image, Point node, int targetColor, int replacementColor) {
            int width = image.getWidth();
            int height = image.getHeight();
            if (targetColor == replacementColor) {
                return;
            }
            Queue<Point> queue = new LinkedList<>();
            do {
                int x = node.x;
                int y = node.y;
                while (x > 0 && image.getPixel(x - 1, y) == targetColor) {
                    x--;
                }
                boolean spanUp = false;
                boolean spanDown = false;
                while (x < width && image.getPixel(x, y) == targetColor) {
                    image.setPixel(x, y, replacementColor);
                    if (!spanUp && y > 0 && image.getPixel(x, y - 1) == targetColor) {
                        queue.add(new Point(x, y - 1));
                        spanUp = true;
                    } else if (spanUp && y > 0 && image.getPixel(x, y - 1) != targetColor) {
                        spanUp = false;
                    }
                    if (!spanDown && y < height - 1 && image.getPixel(x, y + 1) == targetColor) {
                        queue.add(new Point(x, y + 1));
                        spanDown = true;
                    } else if (spanDown && y < height - 1 && image.getPixel(x, y + 1) != targetColor) {
                        spanDown = false;
                    }
                    x++;
                }
            } while ((node = queue.poll()) != null);
        }
    }

    // دالة مساعدة لتحديث DisplayMetrics باستخدام البديل الحديث عند توفره.
    @SuppressWarnings("deprecation")
    private void updateDisplayMetrics(DisplayMetrics displayMetrics) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            WindowMetrics metrics = getWindowManager().getCurrentWindowMetrics();
            Rect bounds = metrics.getBounds();
            displayMetrics.widthPixels = bounds.width();
            displayMetrics.heightPixels = bounds.height();
        } else {
            getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        }
    }
}
