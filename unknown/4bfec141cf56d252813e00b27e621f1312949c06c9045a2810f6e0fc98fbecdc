apply plugin: 'com.android.application'

// Load secrets from properties file
def secretsPropertiesFile = rootProject.file("secrets.properties")
def secretsProperties = new Properties()
if (secretsPropertiesFile.exists()) {
    secretsProperties.load(new FileInputStream(secretsPropertiesFile))
}

android {
    compileSdk 34
    defaultConfig {
        applicationId "com.alwan.kids2025"
        minSdkVersion 23
        targetSdkVersion 34
        versionCode 4
        versionName "4.0"
        multiDexEnabled true

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

        // Add secure build config fields
        buildConfigField "String", "ADMOB_APP_ID", "\"${secretsProperties.getProperty('ADMOB_APP_ID', 'ca-app-pub-3940256099942544~3347511713')}\""
        buildConfigField "String", "ADMOB_BANNER_UNIT_ID", "\"${secretsProperties.getProperty('ADMOB_BANNER_UNIT_ID', 'ca-app-pub-3940256099942544/6300978111')}\""
        buildConfigField "String", "ADMOB_INTERSTITIAL_UNIT_ID", "\"${secretsProperties.getProperty('ADMOB_INTERSTITIAL_UNIT_ID', 'ca-app-pub-3940256099942544/1033173712')}\""
        buildConfigField "String", "ADMOB_APP_OPEN_AD_UNIT_ID", "\"${secretsProperties.getProperty('ADMOB_APP_OPEN_AD_UNIT_ID', 'ca-app-pub-3940256099942544/3419835294')}\""
        buildConfigField "String", "ONESIGNAL_APP_ID", "\"${secretsProperties.getProperty('ONESIGNAL_APP_ID', 'your-onesignal-app-id')}\""
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.alwan.kids2025'
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_14
        targetCompatibility JavaVersion.VERSION_14
        // Removed unsupported option: allWarningsAsErrors
    }

    tasks.withType(JavaCompile) {
        options.compilerArgs << "-Xlint:deprecation"
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'org.jetbrains:annotations:26.0.2'

    // Testing dependencies
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:5.8.0'
    testImplementation 'org.robolectric:robolectric:4.11.1'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation('androidx.test.espresso:espresso-core:3.5.1', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })
    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test:rules:1.5.0'

    // AndroidX Core Libraries (Updated to latest stable versions)
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.activity:activity:1.8.2'
    implementation 'androidx.fragment:fragment:1.6.2'
    implementation 'androidx.lifecycle:lifecycle-viewmodel:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata:2.7.0'

    // UI Components
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'

    // Preferences
    implementation 'androidx.preference:preference:1.2.1'

    // Image Loading and Processing
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'

    // Splash Screen
    implementation 'com.github.ViksaaSkool:AwesomeSplash:v1.0.0'

    // Color Picker
    implementation 'com.github.QuadFlask:colorpicker:0.0.15'

    // Google Play Services and Ads (Updated versions)
    implementation ('com.google.android.gms:play-services-ads:22.6.0') {
        exclude group: 'com.google.android.gms', module : 'play-services-ads-identifier'
    }
    implementation 'com.google.android.ump:user-messaging-platform:2.2.0'
    implementation 'com.google.android.play:review:2.0.1'
    implementation 'com.google.android.play:app-update:2.1.0'

    // Firebase (Updated BoM)
    implementation platform('com.google.firebase:firebase-bom:32.7.1')
    implementation ('com.google.firebase:firebase-messaging') {
        exclude group: 'com.google.android.gms', module : 'play-services-ads-identifier'
    }
    implementation ('com.google.firebase:firebase-analytics') {
        exclude group: 'com.google.android.gms', module : 'play-services-ads-identifier'
    }
    implementation 'com.google.firebase:firebase-crashlytics'

    // OneSignal (Updated version)
    implementation 'com.onesignal:OneSignal:4.8.7'

    // Security
    implementation 'androidx.security:security-crypto:1.1.0-alpha06'

    // Memory Leak Detection (Debug only)
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.12'
}
apply plugin: 'com.google.gms.google-services'
