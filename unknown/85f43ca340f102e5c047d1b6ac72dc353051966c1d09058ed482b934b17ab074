package com.alwan.kids2025.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Utility class for efficient image loading and processing
 * Handles memory optimization and bitmap recycling
 */
public class ImageUtils {
    private static final String TAG = "ImageUtils";
    private static final int MAX_BITMAP_SIZE = 2048; // Maximum bitmap dimension

    /**
     * Load and scale bitmap efficiently to prevent OutOfMemoryError
     * @param context Application context
     * @param resourceId Resource ID of the image
     * @param targetWidth Target width for scaling
     * @param targetHeight Target height for scaling
     * @return Scaled bitmap or null if loading fails
     */
    @Nullable
    public static Bitmap loadScaledBitmap(@NonNull Context context, int resourceId, 
                                         int targetWidth, int targetHeight) {
        try {
            // First, get image dimensions without loading the full bitmap
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            BitmapFactory.decodeResource(context.getResources(), resourceId, options);
            
            // Calculate sample size to reduce memory usage
            options.inSampleSize = calculateInSampleSize(options, targetWidth, targetHeight);
            options.inJustDecodeBounds = false;
            options.inPreferredConfig = Bitmap.Config.RGB_565; // Use less memory
            
            // Load the bitmap with calculated sample size
            Bitmap originalBitmap = BitmapFactory.decodeResource(context.getResources(), resourceId, options);
            
            if (originalBitmap == null) {
                Log.e(TAG, "Failed to decode bitmap from resource: " + resourceId);
                return null;
            }
            
            // Scale to exact target size if needed
            Bitmap scaledBitmap = scaleBitmapToSize(originalBitmap, targetWidth, targetHeight);
            
            // Recycle original if it's different from scaled
            if (scaledBitmap != originalBitmap) {
                originalBitmap.recycle();
            }
            
            return scaledBitmap;
            
        } catch (OutOfMemoryError e) {
            Log.e(TAG, "OutOfMemoryError loading bitmap: " + resourceId, e);
            System.gc(); // Suggest garbage collection
            return null;
        } catch (Exception e) {
            Log.e(TAG, "Error loading bitmap: " + resourceId, e);
            return null;
        }
    }

    /**
     * Calculate optimal sample size for bitmap loading
     * @param options BitmapFactory options with image dimensions
     * @param reqWidth Required width
     * @param reqHeight Required height
     * @return Sample size (power of 2)
     */
    public static int calculateInSampleSize(@NonNull BitmapFactory.Options options, 
                                          int reqWidth, int reqHeight) {
        final int height = options.outHeight;
        final int width = options.outWidth;
        int inSampleSize = 1;

        if (height > reqHeight || width > reqWidth) {
            final int halfHeight = height / 2;
            final int halfWidth = width / 2;

            // Calculate the largest inSampleSize value that is a power of 2 and keeps both
            // height and width larger than the requested height and width.
            while ((halfHeight / inSampleSize) >= reqHeight
                    && (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2;
            }
        }

        return inSampleSize;
    }

    /**
     * Scale bitmap to exact target size
     * @param source Source bitmap
     * @param targetWidth Target width
     * @param targetHeight Target height
     * @return Scaled bitmap
     */
    @Nullable
    public static Bitmap scaleBitmapToSize(@NonNull Bitmap source, int targetWidth, int targetHeight) {
        if (source.getWidth() == targetWidth && source.getHeight() == targetHeight) {
            return source; // Already correct size
        }
        
        try {
            return Bitmap.createScaledBitmap(source, targetWidth, targetHeight, true);
        } catch (OutOfMemoryError e) {
            Log.e(TAG, "OutOfMemoryError scaling bitmap", e);
            return source; // Return original if scaling fails
        }
    }

    /**
     * Get optimal display dimensions for the current device
     * @param context Application context
     * @return int array with [width, height]
     */
    @NonNull
    public static int[] getOptimalDisplaySize(@NonNull Context context) {
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics displayMetrics = new DisplayMetrics();
        
        if (windowManager != null) {
            windowManager.getDefaultDisplay().getMetrics(displayMetrics);
        }
        
        // Use 70% of screen size for optimal performance
        int width = (int) (displayMetrics.widthPixels * 0.7);
        int height = (int) (displayMetrics.heightPixels * 0.5);
        
        // Ensure dimensions don't exceed maximum
        width = Math.min(width, MAX_BITMAP_SIZE);
        height = Math.min(height, MAX_BITMAP_SIZE);
        
        return new int[]{width, height};
    }

    /**
     * Create a copy of bitmap that can be modified
     * @param source Source bitmap
     * @return Mutable copy of the bitmap
     */
    @Nullable
    public static Bitmap createMutableCopy(@NonNull Bitmap source) {
        try {
            return source.copy(Bitmap.Config.ARGB_8888, true);
        } catch (OutOfMemoryError e) {
            Log.e(TAG, "OutOfMemoryError creating mutable copy", e);
            return null;
        }
    }

    /**
     * Safely recycle bitmap if not null and not recycled
     * @param bitmap Bitmap to recycle
     */
    public static void recycleBitmap(@Nullable Bitmap bitmap) {
        if (bitmap != null && !bitmap.isRecycled()) {
            bitmap.recycle();
        }
    }

    /**
     * Check if bitmap is valid (not null and not recycled)
     * @param bitmap Bitmap to check
     * @return true if bitmap is valid
     */
    public static boolean isValidBitmap(@Nullable Bitmap bitmap) {
        return bitmap != null && !bitmap.isRecycled();
    }

    /**
     * Combine two bitmaps into one
     * @param background Background bitmap
     * @param overlay Overlay bitmap
     * @return Combined bitmap
     */
    @Nullable
    public static Bitmap combineBitmaps(@NonNull Bitmap background, @NonNull Bitmap overlay) {
        try {
            Bitmap combined = Bitmap.createBitmap(background.getWidth(), background.getHeight(), 
                                                background.getConfig());
            Canvas canvas = new Canvas(combined);
            canvas.drawBitmap(background, new Matrix(), null);
            canvas.drawBitmap(overlay, new Matrix(), null);
            return combined;
        } catch (OutOfMemoryError e) {
            Log.e(TAG, "OutOfMemoryError combining bitmaps", e);
            return background; // Return background if combining fails
        }
    }
}
