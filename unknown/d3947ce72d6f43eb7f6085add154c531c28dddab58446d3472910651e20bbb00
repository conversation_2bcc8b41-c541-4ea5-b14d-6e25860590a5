package com.alwan.kids2025.utils;

import android.app.ActivityManager;
import android.content.Context;
import android.graphics.Bitmap;
import android.util.Log;
import android.util.LruCache;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * Utility class for memory management and optimization
 * Provides bitmap caching and memory monitoring
 */
public class MemoryUtils {
    private static final String TAG = "MemoryUtils";
    private static final int CACHE_SIZE_PERCENTAGE = 12; // 12% of available memory for cache
    
    private static LruCache<String, Bitmap> bitmapCache;
    private static final List<WeakReference<Bitmap>> bitmapReferences = new ArrayList<>();

    /**
     * Initialize memory management system
     * @param context Application context
     */
    public static void initialize(@NonNull Context context) {
        if (bitmapCache == null) {
            int cacheSize = calculateCacheSize(context);
            bitmapCache = new LruCache<String, Bitmap>(cacheSize) {
                @Override
                protected int sizeOf(String key, Bitmap bitmap) {
                    return getBitmapSize(bitmap);
                }

                @Override
                protected void entryRemoved(boolean evicted, String key, Bitmap oldValue, Bitmap newValue) {
                    if (evicted && oldValue != null && !oldValue.isRecycled()) {
                        Log.d(TAG, "Bitmap evicted from cache: " + key);
                    }
                }
            };
            Log.d(TAG, "Bitmap cache initialized with size: " + cacheSize + " bytes");
        }
    }

    /**
     * Calculate optimal cache size based on available memory
     * @param context Application context
     * @return Cache size in bytes
     */
    private static int calculateCacheSize(@NonNull Context context) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        if (activityManager != null) {
            int memoryClass = activityManager.getMemoryClass();
            return (1024 * 1024 * memoryClass) / 100 * CACHE_SIZE_PERCENTAGE;
        }
        return 4 * 1024 * 1024; // Default 4MB cache
    }

    /**
     * Get bitmap size in bytes
     * @param bitmap Bitmap to measure
     * @return Size in bytes
     */
    public static int getBitmapSize(@NonNull Bitmap bitmap) {
        return bitmap.getAllocationByteCount();
    }

    /**
     * Add bitmap to cache
     * @param key Cache key
     * @param bitmap Bitmap to cache
     */
    public static void addBitmapToCache(@NonNull String key, @NonNull Bitmap bitmap) {
        if (bitmapCache != null && getBitmapFromCache(key) == null) {
            bitmapCache.put(key, bitmap);
            trackBitmap(bitmap);
            Log.d(TAG, "Bitmap added to cache: " + key + " (" + getBitmapSize(bitmap) + " bytes)");
        }
    }

    /**
     * Get bitmap from cache
     * @param key Cache key
     * @return Cached bitmap or null
     */
    @Nullable
    public static Bitmap getBitmapFromCache(@NonNull String key) {
        if (bitmapCache != null) {
            Bitmap bitmap = bitmapCache.get(key);
            if (bitmap != null && !bitmap.isRecycled()) {
                Log.d(TAG, "Bitmap retrieved from cache: " + key);
                return bitmap;
            } else if (bitmap != null && bitmap.isRecycled()) {
                // Remove recycled bitmap from cache
                bitmapCache.remove(key);
                Log.w(TAG, "Removed recycled bitmap from cache: " + key);
            }
        }
        return null;
    }

    /**
     * Remove bitmap from cache
     * @param key Cache key
     */
    public static void removeBitmapFromCache(@NonNull String key) {
        if (bitmapCache != null) {
            Bitmap removed = bitmapCache.remove(key);
            if (removed != null) {
                Log.d(TAG, "Bitmap removed from cache: " + key);
            }
        }
    }

    /**
     * Clear all cached bitmaps
     */
    public static void clearCache() {
        if (bitmapCache != null) {
            bitmapCache.evictAll();
            Log.d(TAG, "Bitmap cache cleared");
        }
    }

    /**
     * Track bitmap reference for memory monitoring
     * @param bitmap Bitmap to track
     */
    private static void trackBitmap(@NonNull Bitmap bitmap) {
        synchronized (bitmapReferences) {
            bitmapReferences.add(new WeakReference<>(bitmap));
        }
    }

    /**
     * Get current memory usage information
     * @param context Application context
     * @return Memory info string
     */
    @NonNull
    public static String getMemoryInfo(@NonNull Context context) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        if (activityManager != null) {
            ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
            activityManager.getMemoryInfo(memoryInfo);
            
            long availableMemory = memoryInfo.availMem / (1024 * 1024); // MB
            long totalMemory = memoryInfo.totalMem / (1024 * 1024); // MB
            long usedMemory = totalMemory - availableMemory;
            
            return String.format("Memory: %dMB used / %dMB total (%.1f%% used)", 
                    usedMemory, totalMemory, (usedMemory * 100.0f / totalMemory));
        }
        return "Memory info unavailable";
    }

    /**
     * Check if device is running low on memory
     * @param context Application context
     * @return true if low memory
     */
    public static boolean isLowMemory(@NonNull Context context) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        if (activityManager != null) {
            ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
            activityManager.getMemoryInfo(memoryInfo);
            return memoryInfo.lowMemory;
        }
        return false;
    }

    /**
     * Force garbage collection and clean up weak references
     */
    public static void performMemoryCleanup() {
        // Clean up weak references
        synchronized (bitmapReferences) {
            bitmapReferences.removeIf(ref -> ref.get() == null || ref.get().isRecycled());
        }
        
        // Suggest garbage collection
        System.gc();
        
        Log.d(TAG, "Memory cleanup performed");
    }

    /**
     * Get cache statistics
     * @return Cache stats string
     */
    @NonNull
    public static String getCacheStats() {
        if (bitmapCache != null) {
            return String.format("Cache: %d hits, %d misses, %d evictions, %d items", 
                    bitmapCache.hitCount(), bitmapCache.missCount(), 
                    bitmapCache.evictionCount(), bitmapCache.size());
        }
        return "Cache not initialized";
    }

    /**
     * Check if memory cleanup is needed
     * @param context Application context
     * @return true if cleanup recommended
     */
    public static boolean shouldPerformCleanup(@NonNull Context context) {
        return isLowMemory(context) || (bitmapCache != null && bitmapCache.size() > bitmapCache.maxSize() * 0.8);
    }

    /**
     * Generate cache key for bitmap
     * @param resourceId Resource ID
     * @param width Target width
     * @param height Target height
     * @return Cache key
     */
    @NonNull
    public static String generateCacheKey(int resourceId, int width, int height) {
        return "bitmap_" + resourceId + "_" + width + "x" + height;
    }

    /**
     * Log memory statistics (debug only)
     * @param context Application context
     */
    public static void logMemoryStats(@NonNull Context context) {
        if (Log.isLoggable(TAG, Log.DEBUG)) {
            Log.d(TAG, getMemoryInfo(context));
            Log.d(TAG, getCacheStats());
            
            synchronized (bitmapReferences) {
                int activeBitmaps = 0;
                for (WeakReference<Bitmap> ref : bitmapReferences) {
                    if (ref.get() != null && !ref.get().isRecycled()) {
                        activeBitmaps++;
                    }
                }
                Log.d(TAG, "Active bitmaps: " + activeBitmaps + " / " + bitmapReferences.size());
            }
        }
    }
}
