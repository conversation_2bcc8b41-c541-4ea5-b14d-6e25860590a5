package com.alwan.kids2025.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.security.crypto.EncryptedSharedPreferences;
import androidx.security.crypto.MasterKey;

import com.alwan.kids2025.BuildConfig;

import java.io.IOException;
import java.security.GeneralSecurityException;

/**
 * Utility class for handling security-related operations
 * Provides secure storage and configuration management
 */
public class SecurityUtils {
    private static final String TAG = "SecurityUtils";
    private static final String ENCRYPTED_PREFS_FILE = "secure_prefs";
    
    private static SharedPreferences encryptedPrefs;

    /**
     * Get secure AdMob App ID from BuildConfig
     * @return AdMob App ID
     */
    @NonNull
    public static String getAdMobAppId() {
        return BuildConfig.ADMOB_APP_ID;
    }

    /**
     * Get secure AdMob Banner Unit ID from BuildConfig
     * @return AdMob Banner Unit ID
     */
    @NonNull
    public static String getAdMobBannerUnitId() {
        return BuildConfig.ADMOB_BANNER_UNIT_ID;
    }

    /**
     * Get secure AdMob Interstitial Unit ID from BuildConfig
     * @return AdMob Interstitial Unit ID
     */
    @NonNull
    public static String getAdMobInterstitialUnitId() {
        return BuildConfig.ADMOB_INTERSTITIAL_UNIT_ID;
    }

    /**
     * Get secure AdMob App Open Ad Unit ID from BuildConfig
     * @return AdMob App Open Ad Unit ID
     */
    @NonNull
    public static String getAdMobAppOpenAdUnitId() {
        return BuildConfig.ADMOB_APP_OPEN_AD_UNIT_ID;
    }

    /**
     * Get secure OneSignal App ID from BuildConfig
     * @return OneSignal App ID
     */
    @NonNull
    public static String getOneSignalAppId() {
        return BuildConfig.ONESIGNAL_APP_ID;
    }

    /**
     * Initialize encrypted shared preferences
     * @param context Application context
     * @return true if initialization successful
     */
    public static boolean initializeSecureStorage(@NonNull Context context) {
        try {
            MasterKey masterKey = new MasterKey.Builder(context)
                    .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                    .build();

            encryptedPrefs = EncryptedSharedPreferences.create(
                    context,
                    ENCRYPTED_PREFS_FILE,
                    masterKey,
                    EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                    EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            );
            
            Log.d(TAG, "Secure storage initialized successfully");
            return true;
            
        } catch (GeneralSecurityException | IOException e) {
            Log.e(TAG, "Failed to initialize secure storage", e);
            return false;
        }
    }

    /**
     * Store sensitive data securely
     * @param key Key for the data
     * @param value Value to store
     * @return true if storage successful
     */
    public static boolean storeSecureData(@NonNull String key, @NonNull String value) {
        if (encryptedPrefs == null) {
            Log.e(TAG, "Secure storage not initialized");
            return false;
        }
        
        try {
            encryptedPrefs.edit().putString(key, value).apply();
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Failed to store secure data", e);
            return false;
        }
    }

    /**
     * Retrieve sensitive data securely
     * @param key Key for the data
     * @param defaultValue Default value if key not found
     * @return Retrieved value or default
     */
    @Nullable
    public static String getSecureData(@NonNull String key, @Nullable String defaultValue) {
        if (encryptedPrefs == null) {
            Log.e(TAG, "Secure storage not initialized");
            return defaultValue;
        }
        
        try {
            return encryptedPrefs.getString(key, defaultValue);
        } catch (Exception e) {
            Log.e(TAG, "Failed to retrieve secure data", e);
            return defaultValue;
        }
    }

    /**
     * Remove sensitive data securely
     * @param key Key for the data to remove
     * @return true if removal successful
     */
    public static boolean removeSecureData(@NonNull String key) {
        if (encryptedPrefs == null) {
            Log.e(TAG, "Secure storage not initialized");
            return false;
        }
        
        try {
            encryptedPrefs.edit().remove(key).apply();
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Failed to remove secure data", e);
            return false;
        }
    }

    /**
     * Clear all secure data
     * @return true if clearing successful
     */
    public static boolean clearSecureData() {
        if (encryptedPrefs == null) {
            Log.e(TAG, "Secure storage not initialized");
            return false;
        }
        
        try {
            encryptedPrefs.edit().clear().apply();
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Failed to clear secure data", e);
            return false;
        }
    }

    /**
     * Check if the app is running in debug mode
     * @return true if debug mode
     */
    public static boolean isDebugMode() {
        return BuildConfig.DEBUG;
    }

    /**
     * Validate AdMob configuration
     * @return true if configuration is valid
     */
    public static boolean validateAdMobConfiguration() {
        String appId = getAdMobAppId();
        String bannerId = getAdMobBannerUnitId();
        String interstitialId = getAdMobInterstitialUnitId();
        String appOpenId = getAdMobAppOpenAdUnitId();
        
        boolean isValid = !appId.isEmpty() && !bannerId.isEmpty() && 
                         !interstitialId.isEmpty() && !appOpenId.isEmpty();
        
        if (!isValid) {
            Log.w(TAG, "AdMob configuration validation failed");
        }
        
        return isValid;
    }

    /**
     * Log security event (only in debug mode)
     * @param event Event description
     */
    public static void logSecurityEvent(@NonNull String event) {
        if (isDebugMode()) {
            Log.d(TAG, "Security Event: " + event);
        }
    }
}
