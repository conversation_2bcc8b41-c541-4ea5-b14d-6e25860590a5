package com.alwan.kids2025;

import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.graphics.Rect;
import android.os.Bundle;
import androidx.annotation.LayoutRes;
import com.google.android.material.navigation.NavigationView;
import com.google.android.ump.ConsentInformation;
import com.google.android.ump.UserMessagingPlatform;
import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import android.view.MenuItem;
import android.view.View;
import android.widget.FrameLayout;

public class BaseActivity extends AppCompatActivity implements NavigationView.OnNavigationItemSelectedListener {
    int code;
    private NavigationView navigationView;
    private DrawerLayout fullLayout;
    private Toolbar toolbar;
    private ActionBarDrawerToggle drawerToggle;

    @Override
    public void setContentView(@LayoutRes int layoutResID) {
        // تحميل التخطيط الأساسي المتضمن للـ Navigation Drawer والـ Toolbar
        fullLayout = (DrawerLayout) getLayoutInflater().inflate(R.layout.activity_base, null);
        FrameLayout activityContainer = (FrameLayout) fullLayout.findViewById(R.id.activity_content);
        getLayoutInflater().inflate(layoutResID, activityContainer, true);
        // تعيين التخطيط الكامل كمحتوى للنشاط
        super.setContentView(fullLayout);

        toolbar = (Toolbar) findViewById(R.id.toolbar);
        navigationView = (NavigationView) findViewById(R.id.navigationView);

        if (useToolbar()) {
            setSupportActionBar(toolbar);
        } else {
            toolbar.setVisibility(View.GONE);
        }

        setUpNavView();

        // تسجيل OnBackPressed callback باستخدام OnBackPressedDispatcher
        getOnBackPressedDispatcher().addCallback(this, new androidx.activity.OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                if (fullLayout.isDrawerOpen(GravityCompat.START)) {
                    fullLayout.closeDrawer(GravityCompat.START);
                } else {
                    finish();
                }
            }
        });
    }

    protected boolean useToolbar() {
        return true;
    }

    protected void setUpNavView() {
        navigationView.setNavigationItemSelectedListener(this);
        if (useDrawerToggle()) {
            drawerToggle = new ActionBarDrawerToggle(this, fullLayout, toolbar,
                    R.string.nav_drawer_opened,
                    R.string.nav_drawer_closed);
            fullLayout.addDrawerListener(drawerToggle);
            drawerToggle.syncState();
            fullLayout.addDrawerListener(new DrawerLayout.DrawerListener() {
                @Override
                public void onDrawerSlide(@NonNull View drawerView, float slideOffset) { }

                @Override
                public void onDrawerOpened(@NonNull View drawerView) {
                    if (Categories.mAdView != null) {
                        Categories.mAdView.setVisibility(View.GONE);
                    }
                    if (CategoryItems.mAdView != null) {
                        CategoryItems.mAdView.setVisibility(View.GONE);
                    }
                }

                @Override
                public void onDrawerClosed(@NonNull View drawerView) {
                    if (Categories.mAdView != null) {
                        Categories.mAdView.setVisibility(View.VISIBLE);
                    }
                    if (CategoryItems.mAdView != null) {
                        CategoryItems.mAdView.setVisibility(View.VISIBLE);
                    }
                }

                @Override
                public void onDrawerStateChanged(int newState) { }
            });
        } else if (useToolbar() && getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setHomeAsUpIndicator(getResources()
                    .getDrawable(R.drawable.abc_ic_ab_back_material, null));
        }
    }

    protected boolean useDrawerToggle() {
        return true;
    }

    @Override
    public boolean onNavigationItemSelected(MenuItem menuItem) {
        fullLayout.closeDrawer(GravityCompat.START);
        return onOptionsItemSelected(menuItem);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        switch (id) {
            case R.id.mainmenu:
                code = 1;
                Intent i = new Intent(this, Categories.class);
                i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                i.putExtra("code", code);
                startActivity(i);
                return true;
            case R.id.ads:
                adsSettings();
                return true;
            case R.id.share:
                shareTextUrl();
                return true;
            case R.id.rate:
                String package_name = getPackageName();
                Intent r = new Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=" + package_name));
                startActivity(r);
                return true;
            case R.id.about:
                final AlertDialog.Builder alertDialogBuilder = new AlertDialog.Builder(this);
                alertDialogBuilder.setTitle(getString(R.string.app_name));
                alertDialogBuilder
                        .setMessage(getString(R.string.about_text))
                        .setIcon(R.drawable.small_logo)
                        .setCancelable(false)
                        .setPositiveButton(getString(R.string.ok), new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int id) {
                                dialog.cancel();
                            }
                        });
                AlertDialog alertDialog = alertDialogBuilder.create();
                alertDialog.show();
                return true;
            case R.id.privacyPolicy:
                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse("https://sites.google.com/view/colors-kids"));
                startActivity(intent);
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private void shareTextUrl() {
        String package_name = getPackageName();
        Intent share = new Intent(Intent.ACTION_SEND);
        share.setType("text/plain");
        share.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        share.putExtra(Intent.EXTRA_TEXT, "خلي طفلك يعبّر عن خياله ويرسم بألوان جميلة بطريقة ممتعة وآمنة!\nحمّل التطبيق الآن وخليه يستمتع بساعات من الإبداع والتلوين 🎉💛\nhttps://play.google.com/store/apps/details?id=" + package_name);
        startActivity(Intent.createChooser(share, getString(R.string.Share_The_App)));
    }

    public void adsSettings() {
        ConsentInformation consentInformation = UserMessagingPlatform.getConsentInformation(this);
        consentInformation.reset();
        Intent i4 = new Intent(this, Splash.class);
        i4.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(i4);
    }
}
